/*
 * Copyright 1999-2018 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import React from 'react';
import PropTypes from 'prop-types';
import {
  Button,
  ConfigProvider,
  Dialog,
  Field,
  Form,
  Icon,
  Input,
  Message,
  Pagination,
  Table,
  Tag,
} from '@alifd/next';
import PageTitle from 'components/PageTitle';
import DeleteDialog from 'components/DeleteDialog';
import RegionGroup from 'components/RegionGroup';
import { getParams, request, setParams } from '@/globalLib';
import { GLOBAL_PAGE_SIZE_LIST } from '../../../constants';
import TotalRender from '../../../components/Page/TotalRender';
import './AgentManagement.scss';

@ConfigProvider.config
class AgentManagement extends React.Component {
  static displayName = 'AgentManagement';

  static propTypes = {
    locale: PropTypes.object,
    history: PropTypes.object,
  };

  constructor(props) {
    super(props);
    this.deleteDialog = React.createRef();
    this.field = new Field(this);
    
    this.state = {
      loading: false,
      dataSource: [],
      total: 0,
      pageSize: getParams('pageSize') ? parseInt(getParams('pageSize')) : 10,
      currentPage: getParams('pageNo') ? parseInt(getParams('pageNo')) : 1,
      selectedRowKeys: [],
      selectedRows: [],
      searchName: getParams('searchName') || '',
      nownamespace_name: '',
      nownamespace_id: '',
      nownamespace_desc: '',
    };
  }

  componentDidMount() {
    this.getData();
  }

  setNowNameSpace = (name, id, desc) => {
    this.setState({
      nownamespace_name: name,
      nownamespace_id: id,
      nownamespace_desc: desc,
    });
  };

  cleanAndGetData = (needclean = false) => {
    if (needclean) {
      this.setState({
        searchName: '',
        selectedRowKeys: [],
        selectedRows: [],
      });
      setParams({
        searchName: '',
      });
    }
    this.getData();
  };

  getData = (pageNo = this.state.currentPage) => {
    const { pageSize, searchName } = this.state;
    const { locale = {} } = this.props;
    const namespaceId = getParams('namespace') || '';

    this.setState({ loading: true });

    const data = {
      pageNo: pageNo,
      pageSize: pageSize,
      name: searchName || '',
      search: 'blur',
      namespaceId: namespaceId,
    };

    request({
      url: 'v3/console/ai/a2a/list',
      method: 'get',
      data,
      success: (result) => {
        if (result && result.code === 0) {
          this.setState({
            dataSource: result.data?.pageItems || [],
            total: result.data?.totalCount || 0,
            loading: false,
            currentPage: pageNo,
          });
        } else {
          this.setState({ loading: false });
          Message.error(result?.message || locale.getAgentListFailed || 'Failed to get Agent list');
        }
      },
      error: () => {
        this.setState({ loading: false });
        Message.error(locale.getAgentListFailed || 'Failed to get Agent list');
      },
    });
  };

  handleSearch = () => {
    const searchName = this.field.getValue('searchName') || '';
    this.setState({ searchName, currentPage: 1 }, () => {
      setParams('searchName', searchName);
      setParams('pageNo', '1');
      this.getData(1);
    });
  };

  handlePageChange = (currentPage) => {
    this.setState({ currentPage }, () => {
      setParams('pageNo', String(currentPage));
      this.getData(currentPage);
    });
  };

  handlePageSizeChange = (pageSize) => {
    this.setState({ pageSize, currentPage: 1 }, () => {
      setParams('pageSize', String(pageSize));
      setParams('pageNo', '1');
      this.getData(1);
    });
  };

  handleRowSelectionChange = (selectedRowKeys, selectedRows) => {
    this.setState({ selectedRowKeys, selectedRows });
  };

  handleCreateAgent = () => {
    const namespaceId = getParams('namespace') || 'public';
    this.props.history.push(`/newAgent?namespace=${namespaceId}`);
  };

  handleViewDetail = (record) => {
    const namespaceId = getParams('namespace') || 'public';
    this.props.history.push(`/agentDetail?namespace=${namespaceId}&name=${record.name}`);
  };

  handleEditAgent = (record) => {
    const namespaceId = getParams('namespace') || 'public';
    this.props.history.push(`/newAgent?namespace=${namespaceId}&name=${record.name}&mode=edit`);
  };

  handleDeleteAgent = (record) => {
    const { locale = {} } = this.props;
    Dialog.confirm({
      title: locale.deleteConfirm || 'Delete Confirmation',
      content: (locale.deleteAgentConfirm || 'Are you sure you want to delete Agent "{0}"?').replace('{0}', record.name),
      onOk: () => {
        this.deleteAgent(record);
      },
    });
  };

  deleteAgent = (record) => {
    const { locale = {} } = this.props;
    const namespaceId = getParams('namespace') || '';
    const params = new URLSearchParams();
    params.append('name', record.name);
    if (namespaceId) {
      params.append('namespaceId', namespaceId);
    }

    request({
      method: 'DELETE',
      url: `/v3/console/ai/a2a?${params.toString()}`,
      success: (data) => {
        if (data && data.code === 200) {
          Message.success(locale.deleteSuccess || 'Delete successful');
          this.getData();
        } else {
          Message.error(data?.message || locale.deleteFailed || 'Delete failed');
        }
      },
      error: () => {
        Message.error(locale.deleteFailed || 'Delete failed');
      },
    });
  };

  handleBatchDelete = () => {
    const { selectedRows } = this.state;
    const { locale = {} } = this.props;
    
    if (selectedRows.length === 0) {
      Dialog.alert({
        title: locale.tip || 'Tip',
        content: locale.selectAgentToDelete || 'Please select Agents to delete first',
      });
      return;
    }

    Dialog.confirm({
      title: locale.batchDeleteConfirm || 'Batch Delete Confirmation',
      content: (
        <div>
          <p>{(locale.batchDeleteContent || 'Are you sure you want to delete the following {0} Agents?').replace('{0}', selectedRows.length)}</p>
          <ul>
            {selectedRows.map(row => (
              <li key={row.name}>{row.name}</li>
            ))}
          </ul>
        </div>
      ),
      onOk: () => {
        this.batchDeleteAgents();
      },
    });
  };

  batchDeleteAgents = () => {
    const { selectedRows } = this.state;
    const { locale = {} } = this.props;
    const namespaceId = getParams('namespace') || '';

    const deletePromises = selectedRows.map(row => {
      const params = new URLSearchParams();
      params.append('name', row.name);
      if (namespaceId) {
        params.append('namespaceId', namespaceId);
      }

      return new Promise((resolve, reject) => {
        request({
          method: 'DELETE',
          url: `/v3/console/ai/a2a?${params.toString()}`,
          success: resolve,
          error: reject,
        });
      });
    });

    Promise.all(deletePromises)
      .then(() => {
        Message.success(locale.batchDeleteSuccess || 'Batch delete successful');
        this.setState({ selectedRowKeys: [], selectedRows: [] });
        this.getData();
      })
      .catch(() => {
        Message.error(locale.batchDeleteFailed || 'Batch delete failed');
      });
  };

  formatTime = (timeStr) => {
    if (!timeStr) return '--';
    try {
      const date = new Date(timeStr);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      });
    } catch (e) {
      return timeStr;
    }
  };

  renderOperationColumn = (value, index, record) => {
    const { locale = {} } = this.props;
    return (
      <div className="operation-column">
        <a onClick={() => this.handleViewDetail(record)}>
          {locale.details || 'Details'}
        </a>
        <span>|</span>
        <a onClick={() => this.handleEditAgent(record)}>
          {locale.edit || 'Edit'}
        </a>
        <span>|</span>
        <a onClick={() => this.handleDeleteAgent(record)} className="delete-link">
          {locale.delete || 'Delete'}
        </a>
      </div>
    );
  };

  renderEmptyState = () => {
    const { locale = {} } = this.props;
    return (
      <div className="empty-state">
        <div className="empty-icon">
          <Icon type="inbox" />
        </div>
        <div className="empty-text">
          {locale.noAgentData || 'No Agent data'}
        </div>
        <Button type="primary" onClick={this.handleCreateAgent}>
          {locale.createAgent || 'Create Agent'}
        </Button>
      </div>
    );
  };

  render() {
    const { locale = {} } = this.props;
    const { loading, dataSource, total, pageSize, currentPage, selectedRowKeys } = this.state;

    return (
      <div className="agent-management">
        <PageTitle
          title={locale.agentManagement || 'Agent Management'}
          desc={this.state.nownamespace_desc}
          namespaceId={this.state.nownamespace_id}
          namespaceName={this.state.nownamespace_name}
          nameSpace
        />
        <RegionGroup
          namespaceCallBack={this.cleanAndGetData.bind(this)}
          setNowNameSpace={this.setNowNameSpace.bind(this)}
        />

        <div className="search-form">
          <Form inline field={this.field}>
            <Form.Item label={`${locale.agentName || 'Agent Name'}：`}>
              <Input
                name="searchName"
                placeholder={locale.agentNamePlaceholder || 'Please enter Agent name'}
                style={{ width: 200 }}
                onPressEnter={this.handleSearch}
              />
            </Form.Item>
            <Form.Item>
              <Button type="primary" onClick={this.handleSearch} style={{ marginRight: 16 }}>
                {locale.search || 'Search'}
              </Button>
              <Button type="primary" onClick={this.handleCreateAgent}>
                {locale.createAgent || 'Create Agent'}
              </Button>
            </Form.Item>
          </Form>
        </div>

        <Table
          className="agent-table"
          dataSource={dataSource}
          loading={loading}
          emptyContent={this.renderEmptyState()}
          rowSelection={{
            onChange: this.handleRowSelectionChange,
            selectedRowKeys,
          }}
        >
          <Table.Column
            title={locale.agentName || 'Agent Name'}
            dataIndex="name"
            cell={(value) => <strong>{value || '--'}</strong>}
          />
          <Table.Column
            title={locale.version || 'Active Version'}
            dataIndex="latestPublishedVersion"
            cell={(value) => (
              <Tag type="primary" size="small" className="version-tag">
                {value || '--'}
              </Tag>
            )}
          />
          <Table.Column
            title={locale.ip || 'IP'}
            dataIndex="url"
            cell={(value) => (
              <div style={{ maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                {value ? (
                  <a href={value} target="_blank" rel="noopener noreferrer" title={value}>
                    {value}
                  </a>
                ) : '--'}
              </div>
            )}
          />
          <Table.Column
            title={locale.updateTime || 'Update Time'}
            dataIndex="updatedAt"
            cell={(value) => this.formatTime(value)}
          />
          <Table.Column
            title={locale.operation || 'Operation'}
            cell={this.renderOperationColumn}
            width={120}
          />
        </Table>

        {total > 0 && (
          <div className="batch-operations">
            <div>
              <Button
                warning
                disabled={selectedRowKeys.length === 0}
                onClick={this.handleBatchDelete}
              >
                {locale.delete || 'Delete'} {selectedRowKeys.length > 0 && `(${selectedRowKeys.length})`}
              </Button>
            </div>
            <Pagination
              current={currentPage}
              total={total}
              pageSize={pageSize}
              pageSizeList={GLOBAL_PAGE_SIZE_LIST}
              pageSizePosition="start"
              pageSizeSelector="dropdown"
              totalRender={(total) => <TotalRender total={total} />}
              onChange={this.handlePageChange}
              onPageSizeChange={this.handlePageSizeChange}
            />
          </div>
        )}

        <DeleteDialog ref={this.deleteDialog} />
      </div>
    );
  }
}

export default AgentManagement; 