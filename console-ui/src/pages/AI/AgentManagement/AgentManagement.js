/*
 * Copyright 1999-2018 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import React from 'react';
import PropTypes from 'prop-types';
import {
  Button,
  ConfigProvider,
  Dialog,
  Field,
  Form,
  Icon,
  Input,
  Message,
  Pagination,
  Table,
  Tag,
} from '@alifd/next';
import PageTitle from 'components/PageTitle';
import DeleteDialog from 'components/DeleteDialog';
import { getParams, request, setParams } from '@/globalLib';
import { GLOBAL_PAGE_SIZE_LIST } from '../../../constants';
import TotalRender from '../../../components/Page/TotalRender';
import './AgentManagement.scss';

@ConfigProvider.config
class AgentManagement extends React.Component {
  static displayName = 'AgentManagement';

  static propTypes = {
    locale: PropTypes.object,
    history: PropTypes.object,
  };

  constructor(props) {
    super(props);
    this.deleteDialog = React.createRef();
    this.field = new Field(this);
    
    this.state = {
      loading: false,
      dataSource: [],
      total: 0,
      pageSize: getParams('pageSize') ? parseInt(getParams('pageSize')) : 10,
      currentPage: getParams('pageNo') ? parseInt(getParams('pageNo')) : 1,
      selectedRowKeys: [],
      selectedRows: [],
      searchName: getParams('searchName') || '',
    };
  }

  componentDidMount() {
    this.getData();
  }

  getData = (pageNo = this.state.currentPage) => {
    const { pageSize, searchName } = this.state;
    const namespaceId = getParams('namespace') || 'public';

    this.setState({ loading: true });

    const params = new URLSearchParams();
    if (searchName) {
      params.append('name', searchName);
    }
    params.append('namespaceId', namespaceId);
    params.append('pageNo', pageNo);
    params.append('pageSize', pageSize);

    request({
      url: `/v3/console/ai/a2a/list?${params.toString()}`,
      success: (data) => {
        if (data && data.code === 200) {
          this.setState({
            dataSource: data.data || [],
            total: data.data ? data.data.length : 0,
            loading: false,
            currentPage: pageNo,
          });
        } else {
          this.setState({ loading: false });
          Message.error(data?.message || '获取Agent列表失败');
        }
      },
      error: () => {
        this.setState({ loading: false });
        Message.error('获取Agent列表失败');
      },
    });
  };

  handleSearch = () => {
    const searchName = this.field.getValue('searchName') || '';
    this.setState({ searchName, currentPage: 1 }, () => {
      setParams('searchName', searchName);
      setParams('pageNo', '1');
      this.getData(1);
    });
  };

  handlePageChange = (currentPage) => {
    this.setState({ currentPage }, () => {
      setParams('pageNo', String(currentPage));
      this.getData(currentPage);
    });
  };

  handlePageSizeChange = (pageSize) => {
    this.setState({ pageSize, currentPage: 1 }, () => {
      setParams('pageSize', String(pageSize));
      setParams('pageNo', '1');
      this.getData(1);
    });
  };

  handleRowSelectionChange = (selectedRowKeys, selectedRows) => {
    this.setState({ selectedRowKeys, selectedRows });
  };

  handleCreateAgent = () => {
    const namespaceId = getParams('namespace') || 'public';
    this.props.history.push(`/newAgent?namespace=${namespaceId}`);
  };

  handleViewDetail = (record) => {
    const namespaceId = getParams('namespace') || 'public';
    this.props.history.push(`/agentDetail?namespace=${namespaceId}&name=${record.name}`);
  };

  handleEditAgent = (record) => {
    const namespaceId = getParams('namespace') || 'public';
    this.props.history.push(`/newAgent?namespace=${namespaceId}&name=${record.name}&mode=edit`);
  };

  handleDeleteAgent = (record) => {
    const { locale = {} } = this.props;
    Dialog.confirm({
      title: '删除确认',
      content: `确定要删除Agent "${record.name}" 吗？`,
      onOk: () => {
        this.deleteAgent(record);
      },
    });
  };

  deleteAgent = (record) => {
    const namespaceId = getParams('namespace') || 'public';
    const params = new URLSearchParams();
    params.append('name', record.name);
    params.append('namespaceId', namespaceId);

    request({
      method: 'DELETE',
      url: `/v3/console/ai/a2a?${params.toString()}`,
      success: (data) => {
        if (data && data.code === 200) {
          Message.success('删除成功');
          this.getData();
        } else {
          Message.error(data?.message || '删除失败');
        }
      },
      error: () => {
        Message.error('删除失败');
      },
    });
  };

  handleBatchDelete = () => {
    const { selectedRows } = this.state;
    const { locale = {} } = this.props;
    
    if (selectedRows.length === 0) {
      Dialog.alert({
        title: '提示',
        content: '请先选择要删除的Agent',
      });
      return;
    }

    Dialog.confirm({
      title: '批量删除确认',
      content: (
        <div>
          <p>确定要删除以下 {selectedRows.length} 个Agent吗？</p>
          <ul>
            {selectedRows.map(row => (
              <li key={row.name}>{row.name}</li>
            ))}
          </ul>
        </div>
      ),
      onOk: () => {
        this.batchDeleteAgents();
      },
    });
  };

  batchDeleteAgents = () => {
    const { selectedRows } = this.state;
    const namespaceId = getParams('namespace') || 'public';
    
    const deletePromises = selectedRows.map(row => {
      const params = new URLSearchParams();
      params.append('name', row.name);
      params.append('namespaceId', namespaceId);
      
      return new Promise((resolve, reject) => {
        request({
          method: 'DELETE',
          url: `/v3/console/ai/a2a?${params.toString()}`,
          success: resolve,
          error: reject,
        });
      });
    });

    Promise.all(deletePromises)
      .then(() => {
        Message.success('批量删除成功');
        this.setState({ selectedRowKeys: [], selectedRows: [] });
        this.getData();
      })
      .catch(() => {
        Message.error('批量删除失败');
      });
  };

  formatTime = (timeStr) => {
    if (!timeStr) return '--';
    try {
      const date = new Date(timeStr);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      });
    } catch (e) {
      return timeStr;
    }
  };

  renderOperationColumn = (value, index, record) => {
    const { locale = {} } = this.props;
    return (
      <div className="operation-column">
        <a onClick={() => this.handleViewDetail(record)}>
          详情
        </a>
        <span>|</span>
        <a onClick={() => this.handleEditAgent(record)}>
          编辑
        </a>
        <span>|</span>
        <a onClick={() => this.handleDeleteAgent(record)} className="delete-link">
          删除
        </a>
      </div>
    );
  };

  renderEmptyState = () => {
    return (
      <div className="empty-state">
        <div className="empty-icon">
          <Icon type="inbox" />
        </div>
        <div className="empty-text">
          暂无Agent数据
        </div>
        <Button type="primary" onClick={this.handleCreateAgent}>
          创建Agent
        </Button>
      </div>
    );
  };

  render() {
    const { locale = {} } = this.props;
    const { loading, dataSource, total, pageSize, currentPage, selectedRowKeys } = this.state;

    return (
      <div className="agent-management">
        <PageTitle title="Agent管理" />
        
        <div className="search-form">
          <Form inline field={this.field}>
            <Form.Item label="Agent名称：">
              <Input
                name="searchName"
                placeholder="请输入Agent名称"
                style={{ width: 200 }}
                onPressEnter={this.handleSearch}
              />
            </Form.Item>
            <Form.Item>
              <Button type="primary" onClick={this.handleSearch} style={{ marginRight: 16 }}>
                搜索
              </Button>
              <Button type="primary" onClick={this.handleCreateAgent}>
                创建Agent
              </Button>
            </Form.Item>
          </Form>
        </div>

        <Table
          className="agent-table"
          dataSource={dataSource}
          loading={loading}
          emptyContent={this.renderEmptyState()}
          rowSelection={{
            onChange: this.handleRowSelectionChange,
            selectedRowKeys,
          }}
        >
          <Table.Column
            title="Agent名称"
            dataIndex="name"
            cell={(value) => <strong>{value || '--'}</strong>}
          />
          <Table.Column
            title="生效版本"
            dataIndex="latestPublishedVersion"
            cell={(value) => (
              <Tag type="primary" size="small" className="version-tag">
                {value || '--'}
              </Tag>
            )}
          />
          <Table.Column
            title="IP"
            dataIndex="url"
            cell={(value) => (
              <div style={{ maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                {value ? (
                  <a href={value} target="_blank" rel="noopener noreferrer" title={value}>
                    {value}
                  </a>
                ) : '--'}
              </div>
            )}
          />
          <Table.Column
            title="更新时间"
            dataIndex="updatedAt"
            cell={(value) => this.formatTime(value)}
          />
          <Table.Column
            title="操作"
            cell={this.renderOperationColumn}
            width={120}
          />
        </Table>

        {total > 0 && (
          <div className="batch-operations">
            <div>
              <Button
                warning
                disabled={selectedRowKeys.length === 0}
                onClick={this.handleBatchDelete}
              >
                批量删除 {selectedRowKeys.length > 0 && `(${selectedRowKeys.length})`}
              </Button>
            </div>
            <Pagination
              current={currentPage}
              total={total}
              pageSize={pageSize}
              pageSizeList={GLOBAL_PAGE_SIZE_LIST}
              pageSizePosition="start"
              pageSizeSelector="dropdown"
              totalRender={(total) => <TotalRender total={total} />}
              onChange={this.handlePageChange}
              onPageSizeChange={this.handlePageSizeChange}
            />
          </div>
        )}

        <DeleteDialog ref={this.deleteDialog} />
      </div>
    );
  }
}

export default AgentManagement; 