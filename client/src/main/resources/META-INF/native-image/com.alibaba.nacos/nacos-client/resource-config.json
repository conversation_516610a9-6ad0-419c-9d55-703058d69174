{"resources": {"includes": [{"pattern": "\\QMETA-INF/org/apache/logging/log4j/core/config/plugins/Log4j2Plugins.dat\\E"}, {"pattern": "\\QMETA-INF/services/com.alibaba.nacos.api.config.filter.IConfigFilter\\E"}, {"pattern": "\\QMETA-INF/services/com.alibaba.nacos.api.remote.Payload\\E"}, {"pattern": "\\QMETA-INF/services/com.alibaba.nacos.plugin.auth.spi.client.AbstractClientAuthService\\E"}, {"pattern": "\\QMETA-INF/services/com.oracle.truffle.api.TruffleLanguage$Provider\\E"}, {"pattern": "\\QMETA-INF/services/com.oracle.truffle.api.instrumentation.TruffleInstrument$Provider\\E"}, {"pattern": "\\QMETA-INF/services/io.grpc.LoadBalancerProvider\\E"}, {"pattern": "\\QMETA-INF/services/io.grpc.ManagedChannelProvider\\E"}, {"pattern": "\\QMETA-INF/services/io.grpc.NameResolverProvider\\E"}, {"pattern": "\\QMETA-INF/services/org.apache.logging.log4j.core.util.ContextDataProvider\\E"}, {"pattern": "\\QMETA-INF/services/org.apache.logging.log4j.spi.Provider\\E"}, {"pattern": "\\QMETA-INF/services/org.apache.logging.log4j.util.PropertySource\\E"}, {"pattern": "\\Q\\E"}, {"pattern": "\\Qcom/alibaba/nacos/client/logging/log4j2\\E"}, {"pattern": "\\Qcom/oracle/truffle/nfi/backend/libffi/LibFFILanguage.class\\E"}, {"pattern": "\\Qmockito-extensions/org.mockito.plugins.MemberAccessor\\E"}, {"pattern": "\\Qmockito-extensions/org.mockito.plugins.MockMaker\\E"}, {"pattern": "\\Qnacos-log4j2.xml\\E"}, {"pattern": "\\Qnacos-logback.xml\\E"}, {"pattern": "\\Qnacos-version.txt\\E"}, {"pattern": "\\Qorg/mockito/internal/creation/bytebuddy/MockMethodAdvice$ForEquals.class\\E"}, {"pattern": "\\Qorg/mockito/internal/creation/bytebuddy/MockMethodAdvice$ForHashCode.class\\E"}, {"pattern": "\\Qorg/mockito/internal/creation/bytebuddy/MockMethodAdvice$ForStatic.class\\E"}, {"pattern": "\\Qorg/mockito/internal/creation/bytebuddy/MockMethodAdvice.class\\E"}, {"pattern": "\\Qorg/mockito/internal/creation/bytebuddy/inject/MockMethodDispatcher.raw\\E"}, {"pattern": "\\Qorg/slf4j/impl/StaticLoggerBinder.class\\E"}]}, "bundles": []}