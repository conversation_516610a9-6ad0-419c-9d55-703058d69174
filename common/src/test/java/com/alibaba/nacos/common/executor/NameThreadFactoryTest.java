/*
 * Copyright 1999-2018 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.nacos.common.executor;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

class NameThreadFactoryTest {
    
    @Test
    void test() {
        NameThreadFactory threadFactory = new NameThreadFactory("test");
        Thread t1 = threadFactory.newThread(() -> {
        
        });
        Thread t2 = threadFactory.newThread(() -> {
        
        });
        
        assertEquals("test.0", t1.getName());
        assertEquals("test.1", t2.getName());
    }
    
}
