/*
 * Copyright 1999-2023 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.nacos.core.remote.grpc.filter;

import io.grpc.ServerTransportFilter;

/**
 * Nacos grpc server transport filter.
 *
 * <AUTHOR>
 */
@SuppressWarnings("PMD.AbstractClassShouldStartWithAbstractNamingRule")
public abstract class NacosGrpcServerTransportFilter extends ServerTransportFilter {
    
    public static final String SDK_FILTER = "SDK";
    
    public static final String CLUSTER_FILTER = "CLUSTER";
    
    /**
     * Get the type of Interceptor.
     *
     * @return should be `CLUSTER` or `SDK`
     */
    public abstract String type();
}
